import React from "react";
import { <PERSON>rowser<PERSON><PERSON><PERSON>, Route, Routes } from "react-router-dom";
import AuthLayout from "./pages/AuthLayout";
import EmailVerificationSuccess from "./pages/EmailVerificationSuccess";
import VerifyEmail from "./pages/VerifyEmail";
import Dashboard from "./pages/Dashboard";

const App = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/register" element={<AuthLayout />} />
        <Route
          path="/email-verification-success"
          element={<EmailVerificationSuccess />}
        />
        <Route path="/verify-email/:userId/:token" element={<VerifyEmail />} />
        <Route path="/login" element={<AuthLayout />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/forgot-password" element={<AuthLayout />} />
        <Route path="/reset-password/:userId/:token" element={<AuthLayout />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;
