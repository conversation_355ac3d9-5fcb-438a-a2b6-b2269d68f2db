import { useState } from "react";
import { apiClient } from "@/config/api";

export const useResetPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleResetPassword = async ({ userId, token, password }) => {
  try {
    setLoading(true);
    setError(null);
    setSuccess(false);

    const response = await apiClient.post(
      `/auth/reset-password/${userId}/${token}`,
      { password }
    );

    if (response.data?.success) {
      setSuccess(true);
      toast.success("Password reset successful!");
      return response.data;
    } else {
      const message = response.data?.message || "Failed to reset password";
      setError(message);
      toast.error(message);
      throw new Error(message);
    }
  } catch (err) {
    const errorMessage =
      err.response?.data?.message || err.message || "Something went wrong";
    setError(errorMessage);
    toast.error(errorMessage);
    throw new Error(errorMessage);
  } finally {
    setLoading(false);
  }
};

  return { handleResetPassword, loading, error, success };
};
