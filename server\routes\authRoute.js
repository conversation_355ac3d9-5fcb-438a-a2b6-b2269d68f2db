const express = require("express");
const router = express.Router();
const {
  registerUser,
  resendVerificationEmail,
  verifyEmail,
  loginUser,
  forgotPassword,
  resetPassword,
} = require("../controllers/authController");

router.post("/register", registerUser);
router.post("/resend-verification", resendVerificationEmail);
router.get("/verify-email/:userId/:token", verifyEmail);
router.post("/login", loginUser);
router.post("/forgot-password", forgotPassword);
router.post("/reset-password/:userId/:token", resetPassword);

module.exports = router;
